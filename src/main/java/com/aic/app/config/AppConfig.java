package com.aic.app.config;

import com.aic.app.job.BlockJob;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import com.aic.app.sdk.ApiImpl;
import com.aic.app.sdk.ApiProxyMock;
import com.aic.app.sdk.IApiProxy;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

@Configuration
public class AppConfig {

    @Bean
    @Profile("default")
    public IApiProxy apiProxy() {
        return new ApiProxyMock();
//        return new ApiImpl();
    }

    @Bean
    @Profile("test")
    public IApiProxy apiProxyTest() {
        // return new ApiProxyMock();
       return new ApiImpl();
    }
    
    @Bean
    @Profile("prod")
    public IApiProxy apiProxyProd() {
        return new ApiImpl();
    }


    @Bean
    @Profile("test")
    public BlockJob blockJobTest() {
        return new BlockJob();
    }
//
//    @Bean
//    @Profile("prod")
//    public BlockJob blockJobProd() {
//        return new BlockJob();
//    }

    @Bean
    public PaginationInnerInterceptor paginationInnerInterceptor() {
        return new PaginationInnerInterceptor();
    }
}
