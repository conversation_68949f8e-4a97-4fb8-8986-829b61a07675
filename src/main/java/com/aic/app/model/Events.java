package com.aic.app.model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 事件记录
 * @TableName events
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true)
public class Events extends BaseEntity {
    /**
     * 事件类型
     */
    private String name;

    /**
     * 交易号
     */
    private String txid;

    /**
     * 合约地址
     */
    private String contract;

    /**
     * 区块高度
     */
    private Long height;

    /**
     * 数据
     */
    @TableField(typeHandler = TypeHandler.class)
    private Data data;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 0-待确认 1-有效
     */
    private Integer status;
    
    
    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        private String txid;
        private String user;
        // 从反序列化 pay_amount
        @JsonAlias("pay_amount")
        private String payAmount;
        private String time;
        @JsonAlias("is_white")
        private String isWhite;
        private String contract;

        private Long nonce;
        private String address;
        private String amount;
        @JsonAlias("_type")
        private String type;
        @JsonAlias("stake_amount")
        private String stakeAmount;
        private Long height;
        @JsonAlias
        private String tokenAmount;
        private Integer status;
        
        // BuyInfo event fields
        private Integer pid;
        private String price;
        private String value;
    }
    
    public static class TypeHandler extends JacksonTypeHandler {
        public TypeHandler() {
            super(Data.class);
        }
    }
    
}
