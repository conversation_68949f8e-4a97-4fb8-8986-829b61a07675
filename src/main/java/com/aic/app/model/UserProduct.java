package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class UserProduct {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String orderNo;
    private Long userId;
    private Integer type;
    private Long productId;

    /**
     * 算力
     */
    private BigDecimal power;
    /**
     * 申请金额
     */
    private BigDecimal amount;
    /**
     * 收益
     */
    private BigDecimal profit;
    /**
     * 0-待确认 1-有效，2-已经期，3-已赎回 4-已复投 5-已结束
     */
    private Integer status;
    private Integer day;
    private Integer releaseDay;
    private BigDecimal rate;
    private BigDecimal fee;
    // private BigDecimal feeToken;
    // private BigDecimal price1;
    // private BigDecimal price2;
    // private int source;

    /**
     * 理财时间
     */
    private Date createTime;

    /**
     * 邀请码
     */
    private transient String code;


    public UserProduct(Long userId, String orderNo, Product product, BigDecimal power) {
        this.userId = userId;
        this.orderNo = orderNo;
        this.productId = product.getId();
        this.type = product.getType();
        this.power = power;
        // 默认待确认
        this.status = 1;
        this.day = product.getDay();
        this.releaseDay = 0;
        this.rate = product.getRate();
        this.createTime = new Date();
    }

}
