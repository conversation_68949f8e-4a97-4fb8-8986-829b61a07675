package com.aic.app.service;

import com.aic.app.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RustService implements AutoCloseable {
	
	static {
//		
//		String threadName = Thread.currentThread().getName();
//		long id = Thread.currentThread().getId();
//		System.out.println("threadName=" + threadName + " ,id=" + id);
//		
//		if ("restartedMain".equals(threadName) && id == 39) {
			System.out.println("加载rust_xyc_lib.");
			System.loadLibrary("rust_xyc_lib");	
//		}
	}
	
	@Override
	public void close() throws Exception {
		// 释放动态库
//		System.out.println("释放动态库");
	}

	public static native String hello(String input);
	public native String exit();

	public native String getSignMsg(String address);

	public native boolean checkSignMsg(String address, String sign);

	public native String getVersion();

	public native String getTokenPrice(String tokenAddress);
	
	public native Object getBuySign(String isWhite, String address, String amount);

//	getWithdrawSign(nonce,address,token,to,amount,deadline,_type)
	public native Object getWithdrawSign(String contract, String token, String to, String amount,
			String _type);
	
	public native Object checkWithdraw();
	
	public List<CheckResult> checkWithdrawVo() {
		Object data = checkWithdraw();
		return JsonUtils.fromJson(data.toString(), new TypeReference<List<CheckResult>>(){});
	}
	
	public WithdrawSign getWithdrawSignVo(String contract, String token, String to, String amount,
			String _type) {
		Object data = getWithdrawSign(contract, token, to, amount, _type);
		return JsonUtils.fromJson(data.toString(), WithdrawSign.class);
	}
	
	public BuySign getBuySignVo(String isWhite, String address, String amount) {
		Object data = getBuySign(isWhite, address, amount);
//			System.out.println("data= " + data);
		return JsonUtils.fromJson(data.toString(), BuySign.class);
	}

	public native boolean syncBlock(String idoAddress, String stakeAddress, String nodeAddress, String withdrawAddress);
	
	public native Object getUserInfo(String id);

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class BuySign {
		public int iswhite;
		public String address;
		public String amount;
		public String sign;
		public long nonce;
		public long deadline;
	}
	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class WithdrawSign {
		public String address;
		public String token;
		public String to;
		public String amount;
		public long deadline;
		public String sign;
		public long type;
		public long nonce;
	}
	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@NoArgsConstructor
	public static class CheckResult {
		public long id;
		public boolean result;
		public String txid;

		public CheckResult(long id) {
			this.id = id;
		}
	}
	
	public static void main(String[] args) {
//		RustService rustService = new RustService();
		 System.out.println(RustService.hello("Java"));
		// 计时
		// long start = System.currentTimeMillis();
		// for (int i = 0; i < 100000; i++) {
		// System.out.println(rustService.hello("Java"));;
		// }
		// long end = System.currentTimeMillis();
		// System.out.println("Java调用Rust函数耗时：" + (end - start) + "ms");

//		System.out.println(rustService.getSignMsg("0x01"));
//		System.out.println(rustService.getVersion());
//		System.out.println(rustService.checkSignMsg("0xB5686Faa50e064917593AD753C0C26B7764fDD85",
//				"0x3901e314d8acb954045eaad00ae11c2d457d388b16851ce89e1a193b5a74e1786d7925fff3b0acf792b727476428ccf5466e55087c54d39bc917e496dd9442881c"));
//		System.out.println(rustService.getVersion());
//
//		System.out.println(rustService.getBuySignVo("true", "0xB5686Faa50e064917593AD753C0C26B7764fDD85", "100000000000000000"));
//
////		token:0x8a6556FaA0846d329D470Ce1342236ca2c6609d0
////		ido_router:0x5d7ff39F5794D5a743D05B2786784e6DF9F4e293
////		stake_router:0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca
//		
//		System.out.println(rustService.getWithdrawSignVo("0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca", "0x8a6556FaA0846d329D470Ce1342236ca2c6609d0", "0xB5686Faa50e064917593AD753C0C26B7764fDD85", "10000000000000000000", "1"));
//
//
//		System.out.println(rustService.syncBlock("0x5d7ff39F5794D5a743D05B2786784e6DF9F4e293", "0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca"));

//		System.out.println(rustService.checkWithdrawVo());
		
		
		// Object userInfo = rustService.getUserInfo("1");
		// System.out.println(userInfo);

	}

}
