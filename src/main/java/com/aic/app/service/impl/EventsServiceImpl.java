package com.aic.app.service.impl;

import com.aic.app.mapper.EventsMapper;
import com.aic.app.model.BaseEntity;
import com.aic.app.model.Events;
import com.aic.app.model.Product;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import com.aic.app.service.IEventsService;
import com.aic.app.service.IProductService;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.IUserProductService;
import com.aic.app.service.IUserService;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
@Slf4j
public class EventsServiceImpl extends ServiceImpl<EventsMapper, Events>
    implements IEventsService {

    @Resource
    IProductService productService;
    @Resource
    IStakeUserService stakeUserService;
    @Resource
    IUserService userService;
    @Resource
    IUserProductService userProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleEvent(Events events) {
        if (!events.getStatus().equals(0)) {
            log.warn("[event] 事件已处理 {}", events);
            return;
        }
        switch (events.getName()) {
            case "BuyInfo":
                handleBuyInfoEvent(events);
                break;
            default:
                log.warn("[event] 未处理事件 {}", events.getName());
                return;
        }

        this.update(new LambdaUpdateWrapper<Events>()
                .set(Events::getStatus, 1)
                .eq(BaseEntity::getId, events.getId()));    
        
    }

    /**
     * 处理BuyInfo事件
     * @param events 事件对象
     */
    private void handleBuyInfoEvent(Events events) {
        Events.Data data = events.getData();
        log.info("[event] 处理BuyInfo事件，data = {}", data);
        
        // 验证必要字段
        if (data.getAddress() == null || data.getPid() == null || data.getValue() == null || data.getAmount() == null) {
            log.error("[event] BuyInfo事件数据不完整，address = {}, pid = {}, value = {}, amount = {}", 
                    data.getAddress(), data.getPid(), data.getValue(), data.getAmount());
            return;
        }
        
        // 根据address查询用户，如果不存在则自动创建
        User user = stakeUserService.checkUser(data.getAddress());
        
        // 根据pid查询product
        Product product = productService.getById(data.getPid());
        if (product == null) {
            log.warn("[event] 找不到产品，pid = {}", data.getPid());
            return;
        }
        
        // 验证价格和金额
        try {
            BigDecimal eventValue = new BigDecimal(data.getValue());
            BigDecimal eventAmount = new BigDecimal(data.getAmount().toString());
            BigDecimal productPrice = product.getPrice();
            
            // 计算期望的金额：价格 * 数量
            BigDecimal expectedValue = productPrice.multiply(eventAmount);
            
            // 比较实际金额与期望金额（允许小的精度误差）
            if (eventValue.compareTo(expectedValue) != 0) {
                log.error("[event] 价格验证失败，产品价格 = {}, 数量 = {}, 期望金额 = {}, 实际金额 = {}", 
                        productPrice, eventAmount, expectedValue, eventValue);
                return;
            }
            
            log.info("[event] 价格验证成功，产品价格 = {}, 数量 = {}, 金额 = {}", 
                    productPrice, eventAmount, eventValue);
            
        } catch (NumberFormatException e) {
            log.error("[event] 数值格式错误，value = {}, amount = {}", data.getValue(), data.getAmount(), e);
            return;
        }
        
        // 根据产品类型处理不同的逻辑
        if (product.getType() == 1) {
            // Type 1: 预售节点 - 更新等级并插入订单
            handleType1Product(user, product, data);
        } else if (product.getType() == 2) {
            // Type 2: 普通商品 - 只插入订单，不更新等级
            handleType2Product(user, product, data);
                 } else {
             log.warn("[event] 未知的产品类型，productId = {}, type = {}", product.getId(), product.getType());
         }
     }

     /**
      * 处理Type 1产品（预售节点）- 更新等级并插入订单
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      */
     private void handleType1Product(User user, Product product, Events.Data data) {
         // 更新用户的preLevel字段为product的day字段
         userService.update(new LambdaUpdateWrapper<User>()
                 .set(User::getPreLevel, product.getDay())
                 .eq(User::getId, user.getId()));
         
         log.info("[event] Type1产品 - 更新用户预售等级，userId = {}, preLevel = {}", user.getId(), product.getDay());
         
         // 创建UserProduct记录
         createUserProduct(user, product, data, "Type1预售节点");
     }

     /**
      * 处理Type 2产品（普通商品）- 只插入订单，不更新等级
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      */
     private void handleType2Product(User user, Product product, Events.Data data) {
         log.info("[event] Type2产品 - 只创建订单，不更新等级，userId = {}, productId = {}", user.getId(), product.getId());
         
         // 创建UserProduct记录
         createUserProduct(user, product, data, "Type2普通商品");
     }

     /**
      * 创建用户产品记录
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      * @param productTypeDesc 产品类型描述
      */
     private void createUserProduct(User user, Product product, Events.Data data, String productTypeDesc) {
         try {
             BigDecimal amount = new BigDecimal(data.getAmount().toString());
             UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, amount);
             userProduct.setAmount(amount);
             // userProduct.setTokenId("BNB"); // 根据实际情况设置tokenId
             userProduct.setType(user.getType());
             
             userProductService.save(userProduct);
             
             log.info("[event] {} - 创建用户产品记录，userId = {}, productId = {}, amount = {}", 
                     productTypeDesc, user.getId(), product.getId(), amount);
         } catch (NumberFormatException e) {
             log.error("[event] {} - 金额格式错误，amount = {}", productTypeDesc, data.getAmount(), e);
         }
     }
}




