create database xyc charset = utf8mb4;

drop table if exists xyc.user;
create table xyc.user
(
    id      int(11)              not null,
    type    int(1)  not null default 0 comment '账户类型: 0-普通账户 1-钱包账户',
    address varchar(42) null comment '地址',
    code varchar(32) null comment '我的邀请码',
    pid int(32)  null comment '邀请人ID',
    level int(11) not null default 1 comment '等级',
    pre_level int(11) not null default 1 comment '预售等级',
    lock_level     tinyint(1) default 0                 not null comment '是否锁定等级',
    set_level_time datetime                             null comment '设置等级时间',
    last_time datetime not null default now() comment '最后登录时间',
    create_time    datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '用户表';


drop table if exists xyc.product;
create table xyc.product
(
    id      int              not null auto_increment,
    type int(1) not null comment '类型',
    name varchar(128) not null comment '名称',
    price decimal(25,8) not null comment '价格',
    rate decimal(25,6) not null comment '日收益率',
    fee decimal(25,6) not null comment '手续费',
    day int(11) not null comment '天数',
    image varchar(512) null comment '图片',
    enable bigint(1) not null default 0 comment '是否有效',
    create_time    datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '商品表';

truncate table xyc.product;
-- 插入商品1-6，对应的价格是 100、1000、5000、20000、50000、100000，产品名称为预售1、预售2..预售6，类型为1，rate、fee为0，day为1-6
insert into xyc.product(id, type, name, price, rate, fee, day, enable) values 
(1, 1, '预售1', 100, 0, 0, 1, 1),
(2, 1, '预售2', 1000, 0, 0, 2, 1),
(3, 1, '预售3', 5000, 0, 0, 3, 1),
(4, 1, '预售4', 20000, 0, 0, 4, 1),
(5, 1, '预售5', 50000, 0, 0, 5, 1),
(6, 1, '预售6', 100000, 0, 0, 6, 1);

-- 插入产品7，price 12000 name iPhone16 pro max
insert into xyc.product(id, type, name, price, rate, fee, day, enable) values 
(7, 2, 'iPhone16 pro max', 12000, 0, 0, 1, 1);

-- update image =

drop table if exists xyc.stake_user;
create table xyc.stake_user
(
    id      int(11)              not null auto_increment,
    user_id int(11) not null comment '用户ID',
    token_id   varchar(32) not null comment '资产ID',
    current_amount decimal(25, 8) default 0 not null comment '活期限额',
    pending_amount decimal(25, 8) default 0 not null comment '待确认',
    static_pool decimal(25, 8) default 0 not null comment '静态池子',
    dynamic_pool decimal(25, 8) default 0 not null comment '动态池子',
    today_static decimal(25, 8) default 0 not null comment '今日静态收益',
    total_static decimal(25, 8) default 0 not null comment '累计静态收益',
    today_dynamic decimal(25, 8) default 0 not null comment '今日动态收益',
    total_dynamic decimal(25, 8) default 0 not null comment '累计动态收益',
    today_buy decimal(25, 8) default 0 not null comment '今日购买',
    total_buy decimal(25, 8) default 0 not null comment '累计购买',
    can_receive decimal(25, 8) default 0 not null comment '可领取',
    create_time datetime not null default now() comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '质押用户表';




drop table if exists xyc.user_product;
create table xyc.user_product
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    type int(1) not null comment '类型 0-活期 1-定期',
    order_no varchar(32) not null comment '订单号',
    product_id int(1) not null comment '产品ID',
    power decimal(25,8) not null default 0 comment '释放金额',
    amount decimal(25,8) not null default 0 comment '申请金额',
    profit decimal(25,8) not null default 0 comment '利息',
    fee decimal(25,8) not null default 0 comment '手续费',
    rate decimal(25,6) not null comment '日收益率',
    day int(11) not null comment '天数',
    release_day int(11) not null comment '发放天数',
    create_time datetime not null comment '创建时间',
    status bigint(1) not null default 0 comment '0-待确认 1-有效，2-已经期',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '用户订单表';

alter table xyc.user_product add column price1 decimal(25,8) not null default 0 comment '价格' after fee;
alter table xyc.user_product add column price2 decimal(25,8) not null default 0 comment '价格' after price1;
alter table xyc.user_product add column fee_token decimal(25,8) not null default 0 comment 'Token手续费' after price2;
alter table xyc.user_product add column source int not null default 0 comment '来源' after fee_token;



drop table if exists xyc.user_log;
create table xyc.user_log
(
    id      int              not null auto_increment,
    user_id int(32) not null comment '用户ID',
    type int(2) not null comment '类型',
    product_amount decimal(25,8) not null comment '理财金额',
    amount decimal(25,8) not null comment '金额',
    last_amount decimal(25,8) not null default 0 comment '金额',
    remark varchar(128) null comment '备注',
    symbol varchar(32) DEFAULT 'FTST' null comment '币种',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '用户流水';

drop table if exists xyc.reward_log;
create table xyc.reward_log
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    child_user_id varchar(32) not null comment '用户ID',
    level int(2) not null comment '等级',
    product_amount decimal(25,8) not null default 0 comment '理财数量',
    amount decimal(25,8) not null comment '返佣数量',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '返佣奖励';

alter table xyc.reward_log add column symbol varchar(32) DEFAULT 'FTST' null comment '币种';


truncate table xyc.user;

# insert into xyc.user(id, code, pid, level, limit_amount, current_amount, total_amount) VALUES
# (1,'AAAAAA',null, 3, 1000, 100, 100),
# (2,'BBBBBB',1, 2, 1000, 100, 100),
# (3,'CCCCCC',2, 1, 1000, 100, 100),
# (4,'CCCCCC',3, 0, -1, 500, 2750);


drop table if exists xyc.asset;
create table xyc.asset
(
    id         int         not null auto_increment,
    token_id   varchar(32) not null comment '资产ID',
    token_name varchar(32) not null comment '资产名称',
    ido_address varchar(64) null comment '',
    stake_address varchar(64) null comment '',
    token_address varchar(64) null comment '',
    stake   tinyint(1) not null default 0 comment '是否可以质押',
    primary key (id)
)
    comment '资产';

alter table xyc.asset add column total_staked decimal(25,8) not null default 0 comment '总质押';
alter table xyc.asset add column total_supply decimal(25,8) not null default 0 comment '回购池';
alter table xyc.asset add column total_burn decimal(25,8) not null default 0 comment '销毁池';
alter table xyc.asset add column total_dao decimal(25,8) not null default 0 comment 'dao池';
alter table xyc.asset add column version int(13) not null default 0 comment '版本';
alter table xyc.asset add column total_week_pool decimal(25,8) not null default 0 comment '周分红池';
alter table xyc.asset add column real_week_pool decimal(25,8) not null default 0 comment '周分红池(实发)';

alter table xyc.asset add column logo varchar(512) null comment 'logo' after token_name;
update xyc.asset set logo = 'https://storage.jucoin.online/1/currency/eth.png';



truncate table xyc.asset;
insert into xyc.asset(id, token_id, token_name, stake, ido_address, stake_address, token_address)
VALUES (1, 'BNB', 'BNB', 0, null, null, null),
       (3, 'FIST', 'FIST', 1, '******************************************', '******************************************', '******************************************');

insert into xyc.asset(id, token_id, token_name, stake, ido_address, stake_address, token_address)
VALUES (2, 'JU', 'JU', 0, null, null, null);


drop table if exists xyc.user_asset;
create table xyc.user_asset
(
    id          int                      not null auto_increment,
    user_id     int(11)              not null comment '用户ID',
    token_id    varchar(32)              not null comment '币种',
    balance     decimal(25, 8) default 0 not null comment '余额',
    version     int(11)        default 0 not null comment '版本',
    reason      varchar(128)   default '' null comment '备注',    
    create_time datetime                 not null default now() comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '用户资产';


drop table if exists xyc.asset_transfer;
create table xyc.asset_transfer
(
    id           int                      not null auto_increment,
    type         int(1)                   not null comment '划转类型 0-手机到平台 1-平台到手机',
    order_no       varchar(32)              null comment '订单号',
    user_id      int(11)              not null comment '用户ID',
    token_id     varchar(32)              not null comment '币种',
    quantity     decimal(25, 8) default 0 not null comment '数量',
    status       int(1)         default 0 not null comment '状态 0-待确认 1-成功',
    confirm_time datetime                 null comment '确认时间',
    create_time  datetime                 not null default now() comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '用户划转记录';



drop table if exists xyc.sys_config;
create table xyc.sys_config
(
    id      int              not null auto_increment,
    reward_rate decimal(25,8) not null comment '金额',
    primary key (id)
)
    comment '系统配置表';

insert into xyc.sys_config(id, reward_rate) values (1, 0.002);


-- drop table if exists xyc.reward_day;
-- create table xyc.reward_day
-- (
--     id          int                      not null auto_increment,
--     day     varchar(32)              not null comment '日期',
--     total_static     decimal(25, 8) default 0 not null comment '总静态',
--     total_dynamic     decimal(25, 8) default 0 not null comment '总动态',
--     total_reward     decimal(25, 8) default 0 not null comment '总直推',
--     total_amount     decimal(25, 8) default 0 not null comment '总发放金额',
--     create_time datetime                 not null default now() comment '创建时间',
--     constraint id
--         primary key (id)
-- )
--     comment '每日结算记录';



alter table xyc.reward_log add column product_id int null comment '用户下单ID';
alter table xyc.reward_log add column pay_method varchar(32) null comment '支付方式';
alter table xyc.reward_log add column price decimal(25,8) null comment '币种价格';


drop table if exists xyc.user_relation;
create table xyc.user_relation
(
    id   int(11) not null,
    pid  int(11) null,
    layer int not null default 0,
    path text,
    primary key (id)
);


drop table if exists xyc.project;
create table xyc.project
(
    id   int(11) not null auto_increment,
    name  varchar(64) null,
    description  varchar(64) null,
    pay_token varchar(32) null,
    token varchar(32) null,
    website varchar(256) null,
    twitter varchar(256) null,
    telegram varchar(256) null,
    state int(1) not null default 0 comment '状态：0-未开始 1-已开始 2-已结束',
    enable bit(1) not null default 0 comment '是否启用：0-否 1-是',
    total decimal(25,8) not null default 0 comment '总BNB',
    price decimal(25,8) not null default 0 comment '单价BNB',
    quantity decimal(28,8) not null default 0 comment '已购买数量BNB',
    per_token decimal(28,8) not null default 0 comment '每份多少Token',
    start_time bigint(13) null comment '开始时间',
    presale_time bigint(13) null comment '预售时间',
    end_time bigint(13) null comment '结束时间',
    is_send bit(1) not null default 0 comment '是否发放：0-否 1-是',
    send_time datetime null comment '发放时间',
    create_time datetime not null default now() comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    primary key (id)
);

alter table xyc.project add column logo varchar(512) null comment 'logo' after name;
update xyc.project set logo = 'https://storage.jucoin.online/1/currency/eth.png';

# alter table xyc.project add column whitelist_link varchar(512) null comment '' after per_token;
alter table xyc.project add column info json null comment '' after per_token;
alter table xyc.project add column success bit not null default 0 comment '是否成功' after state;



insert into xyc.project(id, name, description, pay_token, token, website, twitter, telegram, state, enable, total, price, quantity, per_token, start_time, presale_time, end_time) values 
  (1, 'AAA Coin', 'aaa ', 'BNB', 'AAA', '#', '#', '#', 1, 1, 10000, 0.1, 0, 5000, UNIX_TIMESTAMP('2025-04-08 00:00:00'), UNIX_TIMESTAMP('2025-04-08 17:00:00'), UNIX_TIMESTAMP('2025-04-18 00:00:00')),
  (2, 'BBB Coin', 'bbb ', 'BNB', 'BBB', '#', '#', '#', 1, 1, 10000, 0.1, 0, 5000, UNIX_TIMESTAMP('2025-04-09 00:00:00'), UNIX_TIMESTAMP('2025-04-09 17:00:00'), UNIX_TIMESTAMP('2025-04-19 00:00:00'));

drop table if exists xyc.whitelist;
create table xyc.whitelist (
    id int(11) not null auto_increment,
    user_id int(11) not null ,
    project_id int(11) not null ,
    create_time datetime not null default now() comment '创建时间',
    primary key (id)
);

insert into xyc.whitelist(user_id, project_id) values (100002, 1), (100002, 2);


drop table if exists xyc.user_project;
create table xyc.user_project
(
    id   int(11) not null auto_increment,
    user_id      int(11)              not null comment '用户ID',
    project_id      int(11)              not null comment '项目ID',
    state int(1) not null default 0 comment '状态：0-未发币 1-已发币',
    price decimal(25,8) not null default 0 comment '单价',
    quantity int(11) not null default 0 comment '已购买数量',
    create_time datetime not null default now() comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    primary key (id)
);
create unique index user_project_user_id_project_id_uindex
    on xyc.user_project (user_id, project_id);


drop table if exists xyc.user_stake;
create table xyc.user_stake
(
    id      int              not null auto_increment,
    type int(1) not null comment '类型：0-质押 1-解除质押',
    user_id int(11) not null comment '用户ID',
    token_id varchar(32) not null comment 'TOKEN',
    quantity decimal(25,8) not null default 0 comment '数量',
    txid varchar(64) null comment '交易号',
    create_time datetime not null comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status bigint(1) not null default 0 comment '0-待确认 1-有效',
    constraint id
        primary key (id)
)
    comment '用户质押记录';


alter table xyc.stake_user add column week_dynamic decimal(25, 8) default 0 not null comment '周分红' after can_receive;


drop table if exists xyc.events;
create table xyc.events
(
    id      int              not null auto_increment,
    name varchar(64) not null comment '事件类型',
    txid varchar(66) not null comment '交易号',
    contract varchar(66) not null comment '合约地址',
    height bigint(11) not null comment '区块高度',
    data json not null COMMENT '数据',
    create_time datetime DEFAULT CURRENT_TIMESTAMP not null comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status int(1) not null default 0 comment '0-待确认 1-有效',
    constraint id
        primary key (id)
)
    comment '事件记录';


alter table xyc.user_stake
    modify txid varchar(66) null comment '交易号';


drop table if exists xyc.withdraw;
create table xyc.withdraw
(
    id          int auto_increment
        primary key,
    type int(1) not null comment '类型：0 提币、 1 释放、 2 提周分红',
    user_id int(11) not null comment '用户ID',
    token_id varchar(32) not null comment 'TOKEN',
    nonce       int(11) not null comment '随机数',
    address     varchar(66) null,
    token       varchar(66) null,
    sign        text         null,
    quantity      decimal(25,8) null,
    fee         varchar(128) null,
    txid        varchar(128) null,
    data        text         null,
    state       int          null comment '状态 0-待确认 1-成功 2-失败',
    err         varchar(64)  null,
    deadline    bigint       null,
    create_time datetime DEFAULT CURRENT_TIMESTAMP not null comment '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
alter table xyc.withdraw add column contract     varchar(66) null after nonce;
update xyc.withdraw a, xyc.asset b set a.contract = b.stake_address where a.token = b.token_address;

alter table xyc.withdraw add column product_id     int(11) null after sign;

alter table xyc.stake_user add column team_perf decimal(25, 8) default 0 not null comment '团队业绩';
alter table xyc.stake_user add column old_team_perf decimal(25, 8) default 0 not null comment '上周团队业绩';

drop table if exists xyc.price_history;
create table xyc.price_history
(
    id          int auto_increment
        primary key,
    token_id    varchar(32)                        not null comment 'TOKEN',
    price       decimal(25, 8)                     null,
    time        bigint                             null,
    create_time datetime DEFAULT CURRENT_TIMESTAMP not null comment '创建时间'
);

# stake_user 表加一个node int(11) 字段，默认0
alter table xyc.stake_user add column node int(11) default 0 not null comment '节点';
alter table xyc.stake_user add column node_reward decimal(25, 8) default 0 not null comment '节点返佣';
alter table xyc.stake_user add column node_pool decimal(25, 8) default 0 not null comment '节点分红';

# 更新node=0
update xyc.stake_user set node = 0;

drop table if exists xyc.node_product;
create table xyc.node_product
(
    id          int auto_increment primary key,
    token_id    varchar(32)                        not null comment 'TOKEN',
    price       decimal(25, 8)                     null comment '单价',
    parent_rate       decimal(25, 8)                     null comment '返佣比例',
    address       varchar(66)                     null comment '合约地址',
    create_time datetime DEFAULT CURRENT_TIMESTAMP not null comment '创建时间'
);

# asset 表加一个node_address varchar(66) 字段，默认null
alter table xyc.asset add column node_address varchar(66) null comment '节点地址';
# 再加一个node_reward decimal(25, 8) 字段，默认0
alter table xyc.asset add column node_reward decimal(25, 8) default 0 not null comment '节点返佣';

# 再加一个node_price decimal(25, 8) 字段，默认0
alter table xyc.asset add column node_price decimal(25, 8) default 0 not null comment '节点价格';

# 设置AAA为20
update xyc.asset set node_price = 20 where token_id = 'FIST';

# 更新AAA asset 的node_address 为 0x954B76CC70315CF92CF0a2a7Dbf62777022d78b0, node_reward 为 0.2, node_price 为 100
update xyc.asset set node_address = '0x954B76CC70315CF92CF0a2a7Dbf62777022d78b0', node_reward = 0.2 where token_id = 'FIST';


# asset add withdrawAddress 
alter table xyc.asset add column withdraw_address varchar(66) null comment '提现地址';
# set all value 0x7608Edb7a290bfb09134dD1CC02B0c0fF1ae0Eb5
update xyc.asset set withdraw_address = '0x7608Edb7a290bfb09134dD1CC02B0c0fF1ae0Eb5';
# set AAA token_address 0xb477E541d5c3a091e1DdD7f1391c9e7EE392a22F
update xyc.asset set token_address = '0xb477E541d5c3a091e1DdD7f1391c9e7EE392a22F' where token_id = 'FIST';

# userlog add token_id col
alter table xyc.user_log add column token_id varchar(32) null comment 'TOKEN';

# stake_user add maxTeamPerf、nodePerf、maxNodePerf 字段 
alter table xyc.stake_user add column max_team_perf decimal(25, 8) default 0 not null comment '历史最大团队业绩';
alter table xyc.stake_user add column node_perf decimal(25, 8) default 0 not null comment '节点业绩';
alter table xyc.stake_user add column max_node_perf decimal(25, 8) default 0 not null comment '历史最大节点业绩';

alter table xyc.asset add column node_target decimal(25, 8) default 0 not null comment '节点目标业绩';
alter table xyc.asset add column node_rate decimal(25, 8) default 0 not null comment '节点未达业绩拿的比例';

# 更新节点目标业绩为1000000
update xyc.asset set node_target = 1000000;
# 更新节点未达业绩拿的比例为0.5
update xyc.asset set node_rate = 0.5;

# asset 表加一个node_pool decimal(25, 8) 字段，默认0
alter table xyc.asset add column node_pool decimal(25, 8) default 0 not null comment '节点分红';


# stake_user 表新增 sum_amount 伞下业绩
alter table xyc.stake_user add column sum_amount decimal(25, 8) default 0 not null comment '伞下业绩';

alter table xyc.user_product add column fee_token_id varchar(32) null comment '手续费TOKEN';


alter table xyc.asset add column total_supply_bnb decimal(25,8) not null default 0 comment '回购池bnb';

update xyc.sys_config set reward_rate=0.004;

alter table xyc.sys_config add column times json null comment '时间段配置';

update xyc.sys_config set times='["0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004"]';

drop table if exists xyc.biz_log;
CREATE TABLE `biz_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `biz_id` bigint NULL COMMENT '业务ID',
    `operation` varchar(100) NOT NULL COMMENT '操作类型',
    `args` text COMMENT '操作参数',
    `remark` varchar(500) COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_biz_id` (`biz_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务操作日志表';


-- auto-generated definition
create table stake_reward_log
(
    id             int auto_increment
        primary key,
    user_id        int                       not null comment '用户ID',
    child_user_id  int                       not null comment '用户ID',
    level          int                               not null comment '等级',
    product_amount decimal(25, 8) default 0.00000000 not null comment '理财数量',
    amount         decimal(25, 8)                    not null comment '返佣数量',
    create_time    datetime                          not null comment '创建时间',
    symbol         varchar(32)    default 'fist'     null comment '币种',
    product_id     int                               null comment '用户下单ID',
    pay_method     varchar(32)                       null comment '支付方式',
    price          decimal(25, 8)                    null comment '币种价格'
)
    comment '返佣奖励';

